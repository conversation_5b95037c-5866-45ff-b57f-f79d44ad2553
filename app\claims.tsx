import { Colors } from "@/constants/Colors";
import AllowanceForm from "@/forms/claims/allowance-form";
import ExpenseForm from "@/forms/claims/expense-form";
import TravelForm from "@/forms/claims/travel-form";
import { StatusBar } from "expo-status-bar";
import React, { useState } from "react";
import { FlatList, StyleSheet, Text, View } from "react-native";
import { FilterButton } from "./leave";

const statusFilterOptions = [
  { value: "allowance", label: "Allowance" },
  { value: "expense", label: "Expense" },
  { value: "travel", label: "Travel" },
  // { id: "site", label: "Site" },
];

export default function ClaimsScreen() {
  // State to manage the active status filter
  const [activeStatusFilter, setActiveStatusFilter] = useState("allowance"); // Default to "Allowance"

  return (
    <View style={styles.container}>
      <StatusBar style="dark" />
      <View style={styles.filterContainer}>
        {/* Status Filters */}
        <Text style={styles.filterLabel}>Claim Type:</Text>
        <FlatList
          data={statusFilterOptions}
          keyExtractor={(item) => item.value}
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.filterList}
          renderItem={({ item }) => (
            <FilterButton
              option={item}
              isActive={activeStatusFilter === item.value}
              onPress={() => setActiveStatusFilter(item.value)}
            />
          )}
        />
      </View>

      {activeStatusFilter === "allowance" && <AllowanceForm />}
      {activeStatusFilter === "expense" && <ExpenseForm />}
      {activeStatusFilter === "travel" && <TravelForm />}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingHorizontal: 16,
    paddingBottom: 120, // Increased bottom padding
    paddingTop: 20,
  },

  allowanceContainer: {
    marginBottom: 20,
    paddingVertical: 16,
  },
  allowanceHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 16,
  },
  allowanceTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: Colors.text,
  },
  removeButton: {
    padding: 4,
  },
  addMoreButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    padding: 12,
    marginBottom: 20,
  },
  addMoreText: {
    marginLeft: 8,
    fontSize: 16,
    color: Colors.primary,
    fontWeight: "500",
  },
  submitButton: {
    marginTop: 20,
  },
  separator: {
    height: 1,
    backgroundColor: Colors.border,
    marginVertical: 16,
  },
  filterContainer: {
    paddingVertical: 8,
    backgroundColor: Colors.white,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  filterLabel: {
    fontSize: 14,
    fontWeight: "600",
    color: Colors.textSecondary,
    marginLeft: 16,
    marginVertical: 8,
  },
  filterList: {
    paddingHorizontal: 16,
    marginBottom: 8,
  },
});
