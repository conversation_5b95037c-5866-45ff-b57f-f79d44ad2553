import React, { useState } from "react";
import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  Image,
  StyleProp,
  ViewStyle,
  Alert,
  ScrollView,
  ActivityIndicator,
} from "react-native";
import { useController, Control, FieldValues, Path } from "react-hook-form";
import * as ImagePicker from "expo-image-picker";
import { Ionicons } from "@expo/vector-icons";
import { Colors } from "@/constants/Colors";
import { uploadImageToS3 } from "@/lib/s3Upload";

interface FormImagePickerProps<T extends FieldValues> {
  name: Path<T>;
  control: Control<T>;
  label?: string;
  placeholder?: string;
  containerStyle?: StyleProp<ViewStyle>;
  rules?: object;
}

export function FormImagePicker<T extends FieldValues>({
  name,
  control,
  label,
  placeholder = "Take a photo or upload",
  containerStyle,
  rules,
}: FormImagePickerProps<T>) {
  const {
    field,
    fieldState: { error },
  } = useController({
    name,
    control,
    rules,
  });

  const images: string[] = field.value || [];
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState<{
    [key: string]: boolean;
  }>({});

  const handlePickImage = async () => {
    const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
    if (status !== "granted") {
      Alert.alert("Permission Required", "We need access to your gallery.");
      return;
    }

    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ["images"],
      allowsMultipleSelection: true,
      selectionLimit: 0, // 0 means no limit, allows multiple selection
      allowsEditing: false,
      quality: 0.8,
    });

    if (!result.canceled) {
      setUploading(true);
      try {
        const uploadPromises = result.assets.map(async (asset) => {
          setUploadProgress((prev) => ({ ...prev, [asset.uri]: true }));
          try {
            const s3Url = await uploadImageToS3(asset.uri, "form-images");
            setUploadProgress((prev) => ({ ...prev, [asset.uri]: false }));
            return s3Url;
          } catch (error) {
            setUploadProgress((prev) => ({ ...prev, [asset.uri]: false }));
            console.error("Upload failed for", asset.uri, error);
            Alert.alert(
              "Upload Failed",
              `Failed to upload image: ${
                error instanceof Error ? error.message : "Unknown error"
              }`
            );
            return null;
          }
        });

        const uploadedUrls = await Promise.all(uploadPromises);
        const successfulUploads = uploadedUrls.filter(
          (url): url is string => url !== null
        );

        if (successfulUploads.length > 0) {
          field.onChange([...images, ...successfulUploads]);
        }
      } catch (error) {
        console.error("Upload process failed:", error);
        Alert.alert(
          "Upload Failed",
          "Failed to upload images. Please try again."
        );
      } finally {
        setUploading(false);
        setUploadProgress({});
      }
    }
  };

  const handleTakePhoto = async () => {
    const { status } = await ImagePicker.requestCameraPermissionsAsync();
    if (status !== "granted") {
      Alert.alert("Permission Required", "We need camera access.");
      return;
    }

    const result = await ImagePicker.launchCameraAsync({
      mediaTypes: ["images"],
      allowsEditing: true,
      aspect: [4, 3],
      quality: 0.8,
    });

    if (!result.canceled) {
      setUploading(true);
      const asset = result.assets[0];
      setUploadProgress((prev) => ({ ...prev, [asset.uri]: true }));

      try {
        const s3Url = await uploadImageToS3(asset.uri, "form-images");
        field.onChange([...images, s3Url]);
      } catch (error) {
        console.error("Upload failed for camera photo:", error);
        Alert.alert(
          "Upload Failed",
          `Failed to upload photo: ${
            error instanceof Error ? error.message : "Unknown error"
          }`
        );
      } finally {
        setUploading(false);
        setUploadProgress((prev) => ({ ...prev, [asset.uri]: false }));
      }
    }
  };

  const handleRemoveImage = (uriToRemove: string) => {
    field.onChange(images.filter((uri) => uri !== uriToRemove));
  };

  const showImageSourceOptions = () => {
    Alert.alert("Add Image", "Choose an option", [
      { text: "Take Photo", onPress: handleTakePhoto },
      { text: "Choose from Gallery", onPress: handlePickImage },
      { text: "Cancel", style: "cancel" },
    ]);
  };

  return (
    <View style={[styles.container, containerStyle]}>
      {label && <Text style={styles.label}>{label}</Text>}
      <ScrollView horizontal showsHorizontalScrollIndicator={false}>
        {images.map((uri) => (
          <View key={uri} style={styles.imageWrapper}>
            <Image source={{ uri }} style={styles.image} />
            <TouchableOpacity
              style={styles.removeButton}
              onPress={() => handleRemoveImage(uri)}
            >
              <Ionicons name="close-circle" size={20} color="red" />
            </TouchableOpacity>
          </View>
        ))}
        <TouchableOpacity
          style={[
            styles.addImageButton,
            uploading && styles.addImageButtonDisabled,
          ]}
          onPress={showImageSourceOptions}
          disabled={uploading}
        >
          {uploading ? (
            <>
              <ActivityIndicator size="large" color={Colors.primary} />
              <Text style={styles.uploadText}>Uploading...</Text>
            </>
          ) : (
            <>
              <Ionicons
                name="add-circle-outline"
                size={40}
                color={Colors.primary}
              />
              <Text style={styles.uploadText}>{placeholder}</Text>
            </>
          )}
        </TouchableOpacity>
      </ScrollView>
      {error && <Text style={styles.errorText}>{error.message}</Text>}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  label: {
    fontSize: 16,
    fontWeight: "500",
    color: Colors.text,
    marginBottom: 8,
  },
  imageWrapper: {
    position: "relative",
    marginRight: 10,
  },
  image: {
    width: 100,
    height: 100,
    borderRadius: 8,
  },
  removeButton: {
    position: "absolute",
    top: -5,
    right: -5,
    backgroundColor: "white",
    borderRadius: 10,
  },
  addImageButton: {
    justifyContent: "center",
    alignItems: "center",
    paddingHorizontal: 12,
  },
  uploadText: {
    marginTop: 4,
    fontSize: 12,
    color: Colors.primary,
    textAlign: "center",
  },
  errorText: {
    fontSize: 12,
    color: Colors.error || "red",
    marginTop: 4,
  },
});
