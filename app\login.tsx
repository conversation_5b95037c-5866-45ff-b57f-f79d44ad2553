import React, { useState, useEffect } from "react";
import {
  StyleSheet,
  View,
  Text,
  Image,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  Dimensions,
} from "react-native";
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  withSpring,
  withD<PERSON>y,
  withSequence,
  Easing,
} from "react-native-reanimated";

import { StatusBar } from "expo-status-bar";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Colors } from "@/constants/Colors";
import { Form, FormInput, FormSubmitButton } from "@/components/forms";
import { useSession } from "@/providers/auth-provider";
import { SignInInput } from "@/generated/graphql";
import Logo from "@/constants/Logo";

// Define the login form schema
const loginSchema = z.object({
  phone: z
    .string()
    .min(1, { message: "Phone number is required" })
    .regex(/^\d+$/, { message: "Phone number must contain only digits" }),
  password: z
    .string()
    .min(6, { message: "Password must be at least 6 characters" }),
});

// Type for login form data
type LoginFormData = z.infer<typeof loginSchema>;

// Default values for login form
const defaultLoginValues: LoginFormData = {
  phone: __DEV__ ? "**********" : "",
  password: __DEV__ ? "rafeeq" : "",
};

export default function LoginScreen() {
  const { signIn, isLoading, error: authError } = useSession();

  const [loginError, setLoginError] = useState<string | null>(null);

  // Get screen dimensions for responsive logo sizing
  const { width: screenWidth, height: screenHeight } = Dimensions.get("window");

  // Calculate logo size based on screen dimensions (responsive)
  const logoSize = Math.min(screenWidth * 0.35, screenHeight * 0.2, 160);

  // Animation values
  const logoScale = useSharedValue(0);
  const logoOpacity = useSharedValue(0);

  const appNameTranslateY = useSharedValue(30);
  const appNameOpacity = useSharedValue(0);

  const welcomeTranslateY = useSharedValue(50);
  const welcomeOpacity = useSharedValue(0);

  const subtitleTranslateY = useSharedValue(30);
  const subtitleOpacity = useSharedValue(0);

  const formTranslateY = useSharedValue(40);
  const formOpacity = useSharedValue(0);

  // Animated styles
  const logoAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: logoScale.value }],
    opacity: logoOpacity.value,
  }));

  const appNameAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ translateY: appNameTranslateY.value }],
    opacity: appNameOpacity.value,
  }));

  const welcomeAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ translateY: welcomeTranslateY.value }],
    opacity: welcomeOpacity.value,
  }));

  const subtitleAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ translateY: subtitleTranslateY.value }],
    opacity: subtitleOpacity.value,
  }));

  const formAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ translateY: formTranslateY.value }],
    opacity: formOpacity.value,
  }));

  // Start entrance animations when component mounts
  useEffect(() => {
    // Logo animation - scale up and fade in
    logoScale.value = withDelay(
      200,
      withSpring(1, { damping: 12, stiffness: 100 })
    );
    logoOpacity.value = withDelay(200, withTiming(1, { duration: 800 }));

    // App name animation - slide up and fade in
    appNameTranslateY.value = withDelay(
      600,
      withSpring(0, { damping: 12, stiffness: 100 })
    );
    appNameOpacity.value = withDelay(600, withTiming(1, { duration: 600 }));

    // Welcome text animation - slide up and fade in
    welcomeTranslateY.value = withDelay(
      1000,
      withSpring(0, { damping: 12, stiffness: 100 })
    );
    welcomeOpacity.value = withDelay(1000, withTiming(1, { duration: 600 }));

    // Subtitle animation - slide up and fade in
    subtitleTranslateY.value = withDelay(
      1200,
      withSpring(0, { damping: 12, stiffness: 100 })
    );
    subtitleOpacity.value = withDelay(1200, withTiming(1, { duration: 600 }));

    // Form animation - slide up and fade in
    formTranslateY.value = withDelay(
      1400,
      withSpring(0, { damping: 12, stiffness: 100 })
    );
    formOpacity.value = withDelay(1400, withTiming(1, { duration: 600 }));
  }, []);

  // Navigation is now handled by the AuthenticatedLayout component in _layout.tsx

  // Update local error state when auth error changes
  useEffect(() => {
    if (authError) {
      setLoginError(authError);
    }
  }, [authError]);

  // Initialize form with react-hook-form and zod validation
  const {
    control,
    handleSubmit,
    formState: { isValid },
  } = useForm<LoginFormData>({
    defaultValues: defaultLoginValues,
    resolver: zodResolver(loginSchema),
    mode: "onChange",
  });

  // Handle form submission
  const onSubmit = async (data: LoginFormData) => {
    setLoginError(null);

    try {
      // Call the signIn function from the auth context
      await signIn({
        phone: data.phone,
        password: data.password,
      });
      // Navigation is handled in the auth provider
    } catch (error) {
      // Error handling is done via the authError state
      console.error("Login error:", error);
    }
  };

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === "ios" ? "padding" : "height"}
      keyboardVerticalOffset={Platform.OS === "ios" ? 64 : 0}
    >
      <StatusBar style="dark" />
      <ScrollView
        contentContainerStyle={styles.scrollContent}
        keyboardShouldPersistTaps="handled"
      >
        <View style={styles.logoContainer}>
          {/* Animated Logo with automatic sizing */}
          <Animated.View style={logoAnimatedStyle}>
            <Logo width={logoSize} height={logoSize} />
          </Animated.View>
          <Animated.Text style={[styles.appName, appNameAnimatedStyle]}>
            KKPM
          </Animated.Text>
        </View>

        <View style={styles.formContainer}>
          <Animated.Text style={[styles.welcomeText, welcomeAnimatedStyle]}>
            Welcome
          </Animated.Text>
          <Animated.Text style={[styles.subtitleText, subtitleAnimatedStyle]}>
            Sign in to continue to your account
          </Animated.Text>

          {loginError && <Text style={styles.errorText}>{loginError}</Text>}

          <Animated.View style={formAnimatedStyle}>
            <Form contentContainerStyle={styles.form}>
              {/* Phone Input */}
              <FormInput
                name="phone"
                control={control}
                label="Phone Number"
                placeholder="Enter your phone number"
                keyboardType="phone-pad"
                autoCapitalize="none"
              />

              {/* Password Input */}
              <FormInput
                name="password"
                control={control}
                label="Password"
                placeholder="Enter your password"
                secureTextEntry
                autoCapitalize="none"
                autoComplete="password"
              />

              {/* Submit Button */}
              <FormSubmitButton
                submitLabel="Sign In"
                onSubmit={handleSubmit(onSubmit)}
                isSubmitting={isLoading}
                isValid={isValid}
                style={styles.submitButton}
              />
            </Form>
          </Animated.View>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  scrollContent: {
    flexGrow: 1,
    paddingHorizontal: 16,
    paddingBottom: 40,
    paddingTop: 60,
  },
  logoContainer: {
    alignItems: "center",
    marginBottom: 50,
    paddingTop: 20,
  },
  appName: {
    fontSize: 40,
    fontWeight: "700",
    color: Colors.primary,
    marginTop: 10,
    letterSpacing: 1,
    textAlign: "center",
  },
  formContainer: {
    flex: 1,
  },
  welcomeText: {
    fontSize: 36,
    fontWeight: "800",
    color: Colors.text,
    marginBottom: 12,
    textAlign: "center",
    letterSpacing: 0.5,
  },
  subtitleText: {
    fontSize: 18,
    color: Colors.textSecondary,
    marginBottom: 40,
    textAlign: "center",
    lineHeight: 24,
    fontWeight: "400",
  },
  form: {
    width: "100%",
  },
  submitButton: {
    marginTop: 16,
  },
  errorText: {
    color: Colors.error || "red",
    textAlign: "center",
    marginBottom: 16,
    fontSize: 14,
    maxWidth: "90%",
    alignSelf: "center",
  },
});
