import React, { useState, useEffect } from "react";
import {
  StyleSheet,
  View,
  Text,
  Image,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
} from "react-native";

import { StatusBar } from "expo-status-bar";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Colors } from "@/constants/Colors";
import { Form, FormInput, FormSubmitButton } from "@/components/forms";
import { useSession } from "@/providers/auth-provider";
import { SignInInput } from "@/generated/graphql";

// Define the login form schema
const loginSchema = z.object({
  phone: z
    .string()
    .min(1, { message: "Phone number is required" })
    .regex(/^\d+$/, { message: "Phone number must contain only digits" }),
  password: z
    .string()
    .min(6, { message: "Password must be at least 6 characters" }),
});

// Type for login form data
type LoginFormData = z.infer<typeof loginSchema>;

// Default values for login form
const defaultLoginValues: LoginFormData = {
  phone: __DEV__ ? "**********" : "",
  password: __DEV__ ? "rafeeq" : "",
};

export default function LoginScreen() {
  const { signIn, isLoading, error: authError } = useSession();

  const [loginError, setLoginError] = useState<string | null>(null);

  // Navigation is now handled by the AuthenticatedLayout component in _layout.tsx

  // Update local error state when auth error changes
  useEffect(() => {
    if (authError) {
      setLoginError(authError);
    }
  }, [authError]);

  // Initialize form with react-hook-form and zod validation
  const {
    control,
    handleSubmit,
    formState: { isValid },
  } = useForm<LoginFormData>({
    defaultValues: defaultLoginValues,
    resolver: zodResolver(loginSchema),
    mode: "onChange",
  });

  // Handle form submission
  const onSubmit = async (data: LoginFormData) => {
    setLoginError(null);

    try {
      // Call the signIn function from the auth context
      await signIn({
        phone: data.phone,
        password: data.password,
      });
      // Navigation is handled in the auth provider
    } catch (error) {
      // Error handling is done via the authError state
      console.error("Login error:", error);
    }
  };

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === "ios" ? "padding" : "height"}
      keyboardVerticalOffset={Platform.OS === "ios" ? 64 : 0}
    >
      <StatusBar style="dark" />
      <ScrollView
        contentContainerStyle={styles.scrollContent}
        keyboardShouldPersistTaps="handled"
      >
        <View style={styles.logoContainer}>
          {/* Replace with your app logo */}
          <Image
            source={require("@/assets/images/icon.png")}
            style={styles.logo}
            resizeMode="contain"
          />
          <Text style={styles.appName}>KKPM App</Text>
        </View>

        <View style={styles.formContainer}>
          <Text style={styles.welcomeText}>Welcome</Text>
          <Text style={styles.subtitleText}>
            Sign in to continue to your account
          </Text>

          {loginError && <Text style={styles.errorText}>{loginError}</Text>}

          <Form contentContainerStyle={styles.form}>
            {/* Phone Input */}
            <FormInput
              name="phone"
              control={control}
              label="Phone Number"
              placeholder="Enter your phone number"
              keyboardType="phone-pad"
              autoCapitalize="none"
            />

            {/* Password Input */}
            <FormInput
              name="password"
              control={control}
              label="Password"
              placeholder="Enter your password"
              secureTextEntry
              autoCapitalize="none"
              autoComplete="password"
            />

            {/* Submit Button */}
            <FormSubmitButton
              submitLabel="Sign In"
              onSubmit={handleSubmit(onSubmit)}
              isSubmitting={isLoading}
              isValid={isValid}
              style={styles.submitButton}
            />
          </Form>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  scrollContent: {
    flexGrow: 1,
    paddingHorizontal: 16,
    paddingBottom: 40,
    paddingTop: 60,
  },
  logoContainer: {
    alignItems: "center",
    marginBottom: 40,
  },
  logo: {
    width: 100,
    height: 100,
    marginBottom: 16,
  },
  appName: {
    fontSize: 24,
    fontWeight: "bold",
    color: Colors.primary,
  },
  formContainer: {
    flex: 1,
  },
  welcomeText: {
    fontSize: 28,
    fontWeight: "bold",
    color: Colors.text,
    marginBottom: 8,
    textAlign: "center",
  },
  subtitleText: {
    fontSize: 16,
    color: Colors.textSecondary,
    marginBottom: 32,
    textAlign: "center",
  },
  form: {
    width: "100%",
  },
  submitButton: {
    marginTop: 16,
  },
  errorText: {
    color: Colors.error || "red",
    textAlign: "center",
    marginBottom: 16,
    fontSize: 14,
    maxWidth: "90%",
    alignSelf: "center",
  },
});
