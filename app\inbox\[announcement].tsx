import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
} from "react-native";
import React, { useEffect } from "react";
import { useLocalSearchParams } from "expo-router";
import { useAnnouncementDetailQuery } from "@/generated/graphql";
import LoadingScreen from "@/components/LoadingView";
import { Colors } from "@/constants/Colors";
import { Ionicons } from "@expo/vector-icons";
import { format } from "date-fns";
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  withSpring,
  withDelay,
} from "react-native-reanimated";

export default function InboxDetailScreen() {
  const { announcement } = useLocalSearchParams();
  const { data, isLoading } = useAnnouncementDetailQuery({
    id: announcement as string,
  });

  // Animation values
  const cardScale = useSharedValue(0.9);
  const cardOpacity = useSharedValue(0);
  const headerTranslateY = useSharedValue(-20);
  const contentTranslateY = useSharedValue(20);
  const iconRotation = useSharedValue(0);

  // Animated styles
  const cardAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: cardScale.value }],
    opacity: cardOpacity.value,
  }));

  const headerAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ translateY: headerTranslateY.value }],
  }));

  const contentAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ translateY: contentTranslateY.value }],
  }));

  const iconAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ rotate: `${iconRotation.value}deg` }],
  }));

  // Start animations when data loads
  useEffect(() => {
    if (!isLoading && data?.anouncement) {
      // Card entrance animation
      cardScale.value = withSpring(1, { damping: 15, stiffness: 100 });
      cardOpacity.value = withTiming(1, { duration: 400 });

      // Header animation
      headerTranslateY.value = withDelay(
        200,
        withSpring(0, { damping: 12, stiffness: 100 })
      );

      // Content animation
      contentTranslateY.value = withDelay(
        300,
        withSpring(0, { damping: 12, stiffness: 100 })
      );

      // Icon rotation animation
      iconRotation.value = withDelay(
        400,
        withSpring(360, { damping: 10, stiffness: 80 })
      );
    }
  }, [isLoading, data]);

  if (isLoading) {
    return <LoadingScreen />;
  }

  if (!data?.anouncement) {
    return (
      <View style={styles.container}>
        <View style={styles.errorContainer}>
          <Ionicons
            name="alert-circle-outline"
            size={64}
            color={Colors.error}
          />
          <Text style={styles.errorText}>Announcement not found</Text>
        </View>
      </View>
    );
  }

  const announcementData = data.anouncement;

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      <View style={styles.content}>
        {/* Main Card */}
        <Animated.View style={[styles.card, cardAnimatedStyle]}>
          {/* Header Section */}
          <Animated.View style={[styles.header, headerAnimatedStyle]}>
            <View style={styles.iconContainer}>
              <Animated.View style={iconAnimatedStyle}>
                <Ionicons name="megaphone" size={32} color={Colors.primary} />
              </Animated.View>
            </View>
            <Text style={styles.title}>{announcementData.title}</Text>
          </Animated.View>

          {/* Content Section */}
          <Animated.View style={[styles.cardContent, contentAnimatedStyle]}>
            {/* Date Info */}
            <View style={styles.dateContainer}>
              <Ionicons
                name="calendar-outline"
                size={16}
                color={Colors.textLight}
              />
              <Text style={styles.dateText}>
                Posted on{" "}
                {announcementData.createdAt
                  ? format(
                      new Date(announcementData.createdAt),
                      "MMMM dd, yyyy 'at' h:mm a"
                    )
                  : "Unknown date"}
              </Text>
            </View>

            {/* Description */}
            <View style={styles.descriptionContainer}>
              <Text style={styles.descriptionLabel}>Description</Text>
              <Text style={styles.description}>
                {announcementData.description}
              </Text>
            </View>

            {/* Document Section */}
            {announcementData.document && (
              <TouchableOpacity style={styles.documentContainer}>
                <View style={styles.documentIcon}>
                  <Ionicons
                    name="document-text"
                    size={20}
                    color={Colors.primary}
                  />
                </View>
                <View style={styles.documentInfo}>
                  <Text style={styles.documentTitle}>Attachment</Text>
                  <Text style={styles.documentSubtitle}>
                    Tap to view document
                  </Text>
                </View>
                <Ionicons
                  name="chevron-forward"
                  size={20}
                  color={Colors.textLight}
                />
              </TouchableOpacity>
            )}

            {/* Users Section */}
            {announcementData.users && announcementData.users.length > 0 && (
              <View style={styles.usersContainer}>
                <Text style={styles.usersLabel}>Targeted Users</Text>
                {announcementData.users.map((user) => (
                  <View key={user.id} style={styles.userItem}>
                    <View style={styles.userAvatar}>
                      <Ionicons
                        name="person"
                        size={16}
                        color={Colors.primary}
                      />
                    </View>
                    <View style={styles.userInfo}>
                      <Text style={styles.userName}>{user.fullname}</Text>
                      <Text style={styles.userRole}>{user.role}</Text>
                    </View>
                  </View>
                ))}
              </View>
            )}
          </Animated.View>
        </Animated.View>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  content: {
    padding: 20,
  },
  card: {
    backgroundColor: Colors.white,
    borderRadius: 16,
    padding: 24,
    marginVertical: 10,
    shadowColor: Colors.textLight,
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 8,
  },
  header: {
    alignItems: "center",
    marginBottom: 24,
  },
  iconContainer: {
    width: 64,
    height: 64,
    borderRadius: 32,
    backgroundColor: `${Colors.primary}15`,
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: "700",
    color: Colors.text,
    textAlign: "center",
    lineHeight: 32,
  },
  cardContent: {
    gap: 20,
  },
  dateContainer: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: Colors.lightGray,
    padding: 12,
    borderRadius: 8,
    gap: 8,
  },
  dateText: {
    fontSize: 14,
    color: Colors.textSecondary,
    fontWeight: "500",
  },
  descriptionContainer: {
    gap: 8,
  },
  descriptionLabel: {
    fontSize: 16,
    fontWeight: "600",
    color: Colors.text,
  },
  description: {
    fontSize: 15,
    lineHeight: 24,
    color: Colors.textSecondary,
  },
  documentContainer: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: `${Colors.primary}08`,
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: `${Colors.primary}20`,
    gap: 12,
  },
  documentIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: `${Colors.primary}15`,
    justifyContent: "center",
    alignItems: "center",
  },
  documentInfo: {
    flex: 1,
  },
  documentTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: Colors.text,
  },
  documentSubtitle: {
    fontSize: 14,
    color: Colors.textLight,
    marginTop: 2,
  },
  usersContainer: {
    gap: 12,
  },
  usersLabel: {
    fontSize: 16,
    fontWeight: "600",
    color: Colors.text,
  },
  userItem: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: Colors.lightGray,
    padding: 12,
    borderRadius: 8,
    gap: 12,
  },
  userAvatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: `${Colors.primary}15`,
    justifyContent: "center",
    alignItems: "center",
  },
  userInfo: {
    flex: 1,
  },
  userName: {
    fontSize: 14,
    fontWeight: "600",
    color: Colors.text,
  },
  userRole: {
    fontSize: 12,
    color: Colors.textLight,
    textTransform: "capitalize",
  },
  errorContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    gap: 16,
  },
  errorText: {
    fontSize: 18,
    fontWeight: "600",
    color: Colors.textSecondary,
    textAlign: "center",
  },
});
