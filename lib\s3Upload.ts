import { fetchData } from "@/client";

// Types for S3 upload
export interface SignedUploadUrlInput {
  key: string;
  contentType: string;
  expiresIn?: number;
}

export interface PresignedFields {
  key: string;
  bucket: string;
  XAmzAlgorithm: string;
  XAmzCredential: string;
  XAmzDate: string;
  XAmzSignature: string;
  policy: string;
}

export interface SignedUploadUrl {
  url: string;
  fields: PresignedFields;
}

// GraphQL mutation document
const CREATE_SIGNED_UPLOAD_URL_DOCUMENT = `
  mutation CreateSignedUploadUrl($input: SignedUploadUrlInput!) {
    createSignedUploadUrl(input: $input) {
      url
      fields {
        key
        bucket
        XAmzAlgorithm
        XAmzCredential
        XAmzDate
        XAmzSignature
        policy
      }
    }
  }
`;

/**
 * Get a signed upload URL from the backend
 */
export async function getSignedUploadUrl(
  key: string,
  contentType: string,
  expiresIn: number = 3600
): Promise<SignedUploadUrl> {
  const variables = {
    input: {
      key,
      contentType,
      expiresIn,
    },
  };

  const response = await fetchData<
    { createSignedUploadUrl: SignedUploadUrl },
    { input: SignedUploadUrlInput }
  >(CREATE_SIGNED_UPLOAD_URL_DOCUMENT, variables)();

  return response.createSignedUploadUrl;
}

/**
 * Upload a file to S3 using the signed URL (React Native compatible)
 */
export async function uploadToS3(
  uri: string,
  signedUploadData: SignedUploadUrl,
  fileName?: string
): Promise<string> {
  const formData = new FormData();

  // Add all the fields from the signed URL
  Object.entries(signedUploadData.fields).forEach(([key, value]) => {
    formData.append(key, value);
  });

  // Create file object for React Native
  const fileExtension = getFileExtension(uri);
  const mimeType = getMimeType(fileExtension);
  const finalFileName = fileName || `image.${fileExtension}`;

  // Add the file last - React Native FormData format
  formData.append("file", {
    uri,
    type: mimeType,
    name: finalFileName,
  } as any);

  const response = await fetch(signedUploadData.url, {
    method: "POST",
    body: formData,
  });

  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(`Upload failed: ${response.statusText} - ${errorText}`);
  }

  // Return the S3 URL of the uploaded file
  return `${signedUploadData.url}/${signedUploadData.fields.key}`;
}

/**
 * Generate a unique key for S3 upload
 */
export function generateS3Key(
  prefix: string = "uploads",
  extension: string = "jpg"
): string {
  const timestamp = Date.now();
  const random = Math.random().toString(36).substring(2, 15);
  return `${prefix}/${timestamp}-${random}.${extension}`;
}

/**
 * Get file extension from URI or filename
 */
export function getFileExtension(uri: string): string {
  const extension = uri.split(".").pop()?.toLowerCase();
  return extension || "jpg";
}

/**
 * Get MIME type from file extension
 */
export function getMimeType(extension: string): string {
  const mimeTypes: { [key: string]: string } = {
    jpg: "image/jpeg",
    jpeg: "image/jpeg",
    png: "image/png",
    gif: "image/gif",
    webp: "image/webp",
    bmp: "image/bmp",
  };

  return mimeTypes[extension.toLowerCase()] || "image/jpeg";
}

/**
 * Complete upload process: get signed URL and upload file
 */
export async function uploadImageToS3(
  uri: string,
  prefix: string = "uploads"
): Promise<string> {
  try {
    // Generate unique key and get file info
    const fileExtension = getFileExtension(uri);
    const key = generateS3Key(prefix, fileExtension);
    const contentType = getMimeType(fileExtension);

    // Get signed upload URL
    const signedUploadData = await getSignedUploadUrl(key, contentType);

    // Upload the file
    const s3Url = await uploadToS3(uri, signedUploadData);

    return s3Url;
  } catch (error) {
    console.error("S3 upload error:", error);
    throw new Error(
      `Failed to upload image: ${
        error instanceof Error ? error.message : "Unknown error"
      }`
    );
  }
}
