import React, { useState } from "react";
import { StyleSheet, View, Alert } from "react-native";
import { useRouter } from "expo-router";
import { StatusBar } from "expo-status-bar";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Colors } from "@/constants/Colors";
import { z } from "zod";
import {
  Form,
  FormInput,
  FormSelect,
  FormSubmitButton,
} from "@/components/forms";

import {
  useCreateInventoryRequestMutation,
  useGetInventoryQuery,
} from "@/generated/graphql";
import { createEnumFromArray, enumToOptions } from "@/lib/utils";

interface InventoryItem {
  item: string;
  quantity: number;
  sku: string;
  costPrice: number;
  sellingPrice: number;
}

interface Attribute {
  attibuteName: string;
  attributeValues: string[];
}

interface InventoryProduct {
  _id: string;
  id: string;
  createdAt: string;
  updatedAt: string;
  item: string;
  description: string;
  type: string;
  items: InventoryItem[];
  attributes: Attribute[];
}

interface DropdownOption {
  label: string;
  value: string;
}

export default function UniformRequestScreen() {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { data: inventoryData, isLoading } = useGetInventoryQuery();
  const { mutateAsync: requestFromInventory } =
    useCreateInventoryRequestMutation();

  const getGearSizeEnumForItem = (selectedItemId: string) => {
    const selectedItem = inventoryData?.inventory?.find(
      (item) => item.id === selectedItemId
    );
    console.log(selectedItem);
    return createEnumFromArray(
      selectedItem?.attributes?.[0]?.attributeValues || []
    );
  };

  const gearTypeEnum = createEnumFromArray(
    inventoryData?.inventory?.map((item) => item.item) || []
  );

  const gearSizeEnum = getGearSizeEnumForItem(
    inventoryData?.inventory?.[0]?.id || ""
  );

  const gearTypeOptions = enumToOptions(gearTypeEnum);
  const gearSizeOptions = enumToOptions(gearSizeEnum);

  // Schema for uniform request form
  const uniformRequestSchema = z.object({
    gearType: z.nativeEnum(gearTypeEnum, {
      errorMap: () => ({ message: "Please select a gear type" }),
    }),
    size: z.nativeEnum(gearSizeEnum, {
      errorMap: () => ({ message: "Please select a gear type" }),
    }),
    reason: z
      .string()
      .min(5, { message: "Reason must be at least 5 characters" }),
  });

  type UniformRequestFormData = z.infer<typeof uniformRequestSchema>;

  // Default values for uniform request form
  const defaultUniformRequestValues: UniformRequestFormData = {
    gearType: "",
    size: "",
    reason: "",
  };

  // Initialize form with react-hook-form and zod validation
  const {
    control,
    handleSubmit,
    watch,
    formState: { isValid },
  } = useForm<UniformRequestFormData>({
    defaultValues: defaultUniformRequestValues,
    resolver: zodResolver(uniformRequestSchema),
    mode: "onChange",
  });

  // Handle form submission
  const onSubmit = (data: UniformRequestFormData) => {};

  return (
    <View style={styles.container}>
      <StatusBar style="dark" />

      <Form contentContainerStyle={styles.formContent}>
        {/* Gear Type Dropdown */}
        <FormSelect
          name="gearType"
          control={control}
          label="Select Gear"
          placeholder="Select Value"
          options={gearTypeOptions}
        />

        {/* Size Dropdown */}
        {watch("gearType") && (
          <FormSelect
            name="size"
            control={control}
            label="Select Size"
            placeholder="Select Value"
            options={gearSizeOptions}
          />
        )}

        {/* Reason Input */}
        <FormInput
          name="reason"
          control={control}
          label="Reason"
          placeholder="Enter Reason"
          multiline
          numberOfLines={4}
        />

        {/* Submit Button */}
        <FormSubmitButton
          submitLabel="Request Gear"
          onSubmit={handleSubmit(onSubmit)}
          isSubmitting={isSubmitting}
          isValid={isValid}
          style={styles.submitButton}
          submitButtonStyle={{ backgroundColor: "#7AC142" }} // Green color from the image
        />
      </Form>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  formContent: {
    padding: 16,
    paddingBottom: 80, // Extra bottom padding
    paddingTop: 20,
  },
  submitButton: {
    marginTop: 20,
  },
});
