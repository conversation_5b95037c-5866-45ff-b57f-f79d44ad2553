import React, { useState, useEffect, useMemo } from "react";
import { StyleSheet, View, Alert } from "react-native";
import { useRouter } from "expo-router";
import { StatusBar } from "expo-status-bar";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Colors } from "@/constants/Colors";
import { z } from "zod";
import {
  Form,
  FormInput,
  FormSelect,
  FormSubmitButton,
} from "@/components/forms";

import {
  useCreateInventoryRequestMutation,
  useGetInventoryQuery,
} from "@/generated/graphql";
import { createEnumFromArray, enumToOptions } from "@/lib/utils";

interface InventoryItem {
  item: string;
  quantity: number;
  sku: string;
  costPrice: number;
  sellingPrice: number;
}

interface Attribute {
  attibuteName: string;
  attributeValues: string[];
}

interface InventoryProduct {
  _id: string;
  id: string;
  createdAt: string;
  updatedAt: string;
  item: string;
  description: string;
  type: string;
  items: InventoryItem[];
  attributes: Attribute[];
}

interface DropdownOption {
  label: string;
  value: string;
}

export default function UniformRequestScreen() {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { data: inventoryData, isLoading } = useGetInventoryQuery();
  const { mutateAsync: requestFromInventory } =
    useCreateInventoryRequestMutation();

  // Create gear type options from inventory items
  const gearTypeEnum = createEnumFromArray(
    inventoryData?.inventory?.map((item) => item.item) || []
  );
  const gearTypeOptions = enumToOptions(gearTypeEnum);

  // Schema for uniform request form
  const uniformRequestSchema = z.object({
    gearType: z.nativeEnum(gearTypeEnum, {
      errorMap: () => ({ message: "Please select a gear type" }),
    }),
    size: z.string().min(1, { message: "Please select a size" }),
    reason: z
      .string()
      .min(5, { message: "Reason must be at least 5 characters" }),
  });

  type UniformRequestFormData = z.infer<typeof uniformRequestSchema>;

  // Default values for uniform request form
  const defaultUniformRequestValues: UniformRequestFormData = {
    gearType: "",
    size: "",
    reason: "",
  };

  // Initialize form with react-hook-form and zod validation
  const {
    control,
    handleSubmit,
    watch,
    setValue,
    formState: { isValid },
  } = useForm<UniformRequestFormData>({
    defaultValues: defaultUniformRequestValues,
    resolver: zodResolver(uniformRequestSchema),
    mode: "onChange",
  });

  // Watch the selected gear type to update size options
  const selectedGearType = watch("gearType");

  // Get size options for the selected gear type
  const gearSizeOptions = useMemo(() => {
    if (!selectedGearType) return [];

    const selectedItem = inventoryData?.inventory?.find(
      (item) => item.item === selectedGearType
    );

    if (!selectedItem?.attributes?.[0]?.attributeValues) return [];

    return selectedItem.attributes[0].attributeValues.map((size) => ({
      label: size,
      value: size,
    }));
  }, [selectedGearType, inventoryData]);

  // Reset size when gear type changes
  useEffect(() => {
    if (selectedGearType) {
      setValue("size", "");
    }
  }, [selectedGearType, setValue]);

  // Handle form submission
  const onSubmit = (data: UniformRequestFormData) => {};

  return (
    <View style={styles.container}>
      <StatusBar style="dark" />

      <Form contentContainerStyle={styles.formContent}>
        {/* Gear Type Dropdown */}
        <FormSelect
          name="gearType"
          control={control}
          label="Select Gear"
          placeholder="Select Value"
          options={gearTypeOptions}
        />

        {/* Size Dropdown */}
        {selectedGearType && gearSizeOptions.length > 0 && (
          <FormSelect
            name="size"
            control={control}
            label="Select Size"
            placeholder="Select Value"
            options={gearSizeOptions}
          />
        )}

        {/* Reason Input */}
        <FormInput
          name="reason"
          control={control}
          label="Reason"
          placeholder="Enter Reason"
          multiline
          numberOfLines={4}
        />

        {/* Submit Button */}
        <FormSubmitButton
          submitLabel="Request Gear"
          onSubmit={handleSubmit(onSubmit)}
          isSubmitting={isSubmitting}
          isValid={isValid}
          style={styles.submitButton}
          submitButtonStyle={{ backgroundColor: "#7AC142" }} // Green color from the image
        />
      </Form>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  formContent: {
    padding: 16,
    paddingBottom: 80, // Extra bottom padding
    paddingTop: 20,
  },
  submitButton: {
    marginTop: 20,
  },
});
