import {
  Form,
  FormDatePicker,
  FormImagePicker,
  FormInput,
  FormSelect,
  FormSubmitButton,
} from "@/components/forms";
import { Colors } from "@/constants/Colors";
import { ClaimType, useCreateClaimMutation } from "@/generated/graphql";
import { errorToast, successToast } from "@/lib/utils";
import { useSession } from "@/providers/auth-provider";
import { zodResolver } from "@hookform/resolvers/zod";
import React from "react";
import { useForm } from "react-hook-form";
import { ScrollView, StyleSheet } from "react-native";
import Toast from "react-native-toast-message";
import * as z from "zod";

export enum ExpenseCategory {
  FOOD = "food",
  TRANSPORTATION = "transportation",
  UTILITIES = "utilities",
  ENTERTAINMENT = "entertainment",
  HEALTHCARE = "healthcare",
  EDUCATION = "education",
  HOUSING = "housing",
  OTHER = "other",
}
const getExpenseCategories = () => {
  return Object.entries(ExpenseCategory).map(([key, value]) => ({
    label: key.toLowerCase().replace(/\b\w/g, (l) => l.toUpperCase()),
    value,
  }));
};

const schema = z.object({
  amount: z.coerce.number().min(1, { message: "Amount is required" }),
  purpose: z.string().min(1, { message: "Reason is required" }),
  items: z.array(z.string()).nullable(),
  date: z.coerce.date({ invalid_type_error: "Date is required" }),
  receipts: z.array(z.string()).nullable(),
  claimType: z.nativeEnum(ClaimType),
  user: z.string().min(1),
});

type ExpenseForm = z.infer<typeof schema>;

export default function ExpenseForm() {
  const { session } = useSession();

  const defaultExpenseEntry: z.infer<typeof schema> = {
    amount: 0,
    purpose: "",
    items: null,
    date: undefined as unknown as Date, // Set default date to undefined
    receipts: null,
    claimType: ClaimType.Expense,
    user: session?.fullname as string,
  };
  const { mutateAsync: createExpenseClaim } = useCreateClaimMutation();
  const {
    control,
    handleSubmit,
    reset,
    formState: { errors, isValid },
  } = useForm<ExpenseForm>({
    defaultValues: defaultExpenseEntry,
    resolver: zodResolver(schema),
    mode: "onChange",
  });

  // Handle form submission
  const onSubmit = async (data: ExpenseForm) => {
    try {
      await createExpenseClaim({
        input: {
          ...data,
        },
      });
      successToast("Expense claim submitted successfully!");
      reset(defaultExpenseEntry); // Reset form after successful submission
    } catch (error) {
      errorToast(error);
    }
  };

  // Add a new allowance entry

  return (
    <ScrollView contentContainerStyle={styles.container}>
      <Form>
        {/* Date Input */}
        <FormDatePicker
          name={`date`}
          control={control}
          label="Date"
          placeholder="dd-mm-yy"
        />

        {/* Reason Input */}
        <FormInput
          name={`purpose`}
          control={control}
          label="Purpose"
          placeholder="Enter purpose.."
          multiline
          numberOfLines={3}
        />

        <FormSelect
          name={`items`}
          control={control}
          options={getExpenseCategories()}
          label="Expense Items"
          placeholder="Select"
          multiple={true}
        />

        {/* Amount Input */}
        <FormInput
          name={`amount`}
          control={control}
          label="Amount (RM)"
          placeholder="Enter Amount"
          keyboardType="numeric"
        />

        {/* Upload Invoice */}
        <FormImagePicker
          name={`receipts`}
          control={control}
          label="Upload Receipts"
        />

        {/* Submit Button */}
        <FormSubmitButton
          submitLabel="Submit"
          onSubmit={handleSubmit(onSubmit)}
          isValid={isValid}
          style={styles.submitButton}
        />
      </Form>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
    paddingBottom: 50,
  },
  submitButton: {
    marginTop: 20,
  },
});
