import { Stack, SplashScreen } from "expo-router";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { SafeAreaView, ActivityIndicator, View } from "react-native";
import { useEffect } from "react";
import { useFonts } from "expo-font";
import { Colors } from "@/constants/Colors";
import { SessionProvider, useSession } from "@/providers/auth-provider";
import Toast, { BaseToast, ErrorToast } from "react-native-toast-message";

const toastConfig = {
  /*
    Overwrite 'success' type,
    by modifying the existing `BaseToast` component
  */
  success: (props: any) => (
    <BaseToast
      {...props}
      style={{ borderLeftColor: "pink" }}
      contentContainerStyle={{ paddingHorizontal: 15 }}
      text1Style={{
        fontSize: 30,
        fontWeight: "400",
      }}
    />
  ),
  error: (props: any) => (
    <ErrorToast
      {...props}
      text1Style={{
        fontSize: 17,
      }}
      text2Style={{
        fontSize: 15,
      }}
    />
  ),
};

// Prevent the splash screen from auto-hiding
SplashScreen.preventAutoHideAsync();
export const queryClient = new QueryClient({
  defaultOptions: {
    mutations: {
      onError: (error) => {
        console.error("Mutation error", error);
      },
    },
  },
});

// Inner layout component that handles authentication
function AuthenticatedLayout() {
  const { session, isLoading } = useSession();

  // Show loading indicator while checking authentication
  if (isLoading) {
    return (
      <View style={{ flex: 1, justifyContent: "center", alignItems: "center" }}>
        <ActivityIndicator size="large" color={Colors.primary} />
      </View>
    );
  }

  // Determine which initial route to render based on authentication status
  const initialRouteName = session ? "index" : "login";

  return (
    <Stack
      initialRouteName={initialRouteName}
      screenOptions={{
        headerStyle: {
          backgroundColor: Colors.primary,
        },
        headerTintColor: Colors.white,
        headerShadowVisible: false,
      }}
    >
      {/* Login Screen */}
      <Stack.Screen
        name="login"
        options={{
          headerShown: false,
          // Prevent going back to home if not authenticated
          gestureEnabled: !!session,
        }}
      />

      {/* Home Screen */}
      <Stack.Screen
        name="index"
        options={{
          headerShown: false,
        }}
      />

      {/* Feature Screens */}
      <Stack.Screen name="attendance" options={{ title: "Attendance" }} />
      <Stack.Screen name="leave" options={{ headerShown: false }} />
      <Stack.Screen name="shifts" options={{ headerShown: false }} />
      <Stack.Screen name="payments" options={{ title: "Payments" }} />
      <Stack.Screen name="documents" options={{ headerShown: false }} />
      <Stack.Screen name="calendar" options={{ title: "Calendar" }} />
      <Stack.Screen name="inbox/index" options={{ title: "Inbox" }} />
      <Stack.Screen name="account" options={{ title: "Account" }} />
      <Stack.Screen
        name="personal-profile"
        options={{ title: "Personal Profile" }}
      />
      <Stack.Screen
        name="incident-reporting"
        options={{ title: "Incident Reporting" }}
      />
      <Stack.Screen
        name="uniform-request"
        options={{ title: "Uniform Request" }}
      />
      <Stack.Screen name="claims" options={{ title: "Claims" }} />
      <Stack.Screen
        name="face-registration"
        options={{
          title: "Face Registration",
          headerShown: false,
        }}
      />
    </Stack>
  );
}

/**
 * Root layout component for the application
 * Provides the main navigation structure
 */
export default function RootLayout() {
  // Load fonts
  const [fontsLoaded] = useFonts({
    // You can add custom fonts here if needed
  });

  // Hide splash screen when fonts are loaded
  useEffect(() => {
    if (fontsLoaded) {
      SplashScreen.hideAsync();
    }
  }, [fontsLoaded]);

  // Return null until fonts are loaded
  if (!fontsLoaded) {
    return null;
  }

  return (
    <QueryClientProvider client={queryClient}>
      <SafeAreaView style={{ flex: 1 }}>
        <SessionProvider>
          <AuthenticatedLayout />
          <Toast />
        </SessionProvider>
      </SafeAreaView>
    </QueryClientProvider>
  );
}
/**
 * app/
  ├── shifts/
  │   ├── _layout.tsx
  │   ├── index.tsx
  │   ├── request.tsx
  │   └── [id].tsx
  └── _layout.tsx
 */
