mutation CreateLeave($input: CreateLeaveInput!) {
  createLeave(createLeaveInput: $input) {
    _id
  }
}

query GetLeaves($input: FindLeavesInput!) {
  leaves(filter: $input) {
    _id
    id
    createdAt
    updatedAt
    reason
    leaveType
    startDateTime
    endDateTime
    leaveStatus
    rejectedReason
    user {
      _id
      fullname
      phone
      userStatus
      role
    }
    approvedBy {
      _id
      fullname
      phone
      userStatus
      role
    }
  }
}
